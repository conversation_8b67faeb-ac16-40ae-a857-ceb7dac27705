import { BedrockRuntimeClient, InvokeModelCommand } from '@aws-sdk/client-bedrock-runtime';
import { config } from '../config/environment';
import { ChatMessage, VectorizedDataItem } from '../types/common';

/**
 * AI Service
 * Handles AWS Bedrock integration for conversational AI and text processing
 */
export class AIService {
  private client: BedrockRuntimeClient;
  private readonly modelId: string;
  private readonly embeddingModelId: string;

  constructor() {
    this.client = new BedrockRuntimeClient({
      region: config.aws.region,
      credentials: {
        accessKeyId: config.aws.accessKeyId || '',
        secretAccessKey: config.aws.secretAccessKey || ''
      }
    });
    
    this.modelId = config.aws.bedrock.modelId;
    this.embeddingModelId = config.aws.bedrock.embeddingModelId;
  }

  /**
   * Generate text embeddings using AWS Bedrock
   */
  public async generateEmbedding(text: string): Promise<number[]> {
    try {
      const input = {
        inputText: text
      };

      const command = new InvokeModelCommand({
        modelId: this.embeddingModelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(input)
      });

      const response = await this.client.send(command);
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      
      return responseBody.embedding || [];
    } catch (error) {
      console.error('Error generating embedding:', error);
      throw new Error('Failed to generate text embedding');
    }
  }

  /**
   * Generate AI response using Claude
   */
  public async generateResponse(
    userMessage: string,
    context: VectorizedDataItem[],
    conversationHistory: ChatMessage[] = []
  ): Promise<string> {
    try {
      // Build context from relevant data
      const contextText = this.buildContextFromData(context);
      
      // Build conversation history
      const historyText = conversationHistory
        .slice(-5) // Last 5 messages for context
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');

      // Create the prompt
      const prompt = this.buildPrompt(userMessage, contextText, historyText);
console.log(prompt,"prompt");
      const input = {
        // anthropic_version: "bedrock-2023-05-31",
        max_tokens: config.ai.maxTokens,
        temperature: config.ai.temperature,
        top_k: config.ai.topK,
        top_p: config.ai.topP,
        messages: [
          {
            role: "user",
            content: prompt
          }
        ]
      };

      const command = new InvokeModelCommand({
        modelId: this.modelId,
        contentType: 'application/json',
        accept: 'application/json',
        body: JSON.stringify(input)
      });

      const response = await this.client.send(command);
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      
      return responseBody.content[0].text || 'I apologize, but I could not generate a response.';
    } catch (error) {
      console.error('Error generating AI response:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  /**
   * Build context text from vectorized data items
   */
  private buildContextFromData(items: VectorizedDataItem[]): string {
    if (items.length === 0) {
      return 'No specific information found in the database.';
    }

    const contextParts = items.map(item => {
      const metadata = item.metadata;
      let contextLine = `${item.content}`;
      
      // Add additional context based on type
      if (metadata['branch_name']) {
        contextLine += ` (Available at: ${metadata['branch_name']})`;
      }
      if (metadata['price']) {
        contextLine += ` (Price: ${metadata['price']} AED)`;
      }
      if (metadata['restaurant_name'] && metadata['restaurant_name'] !== 'N/A') {
        contextLine += ` (Restaurant: ${metadata['restaurant_name']})`;
      }
      
      return contextLine;
    });

    return contextParts.join('\n');
  }

  /**
   * Build the complete prompt for the AI
   */
  private buildPrompt(userMessage: string, context: string, history: string): string {
    return `You are a helpful AI assistant for Cravin Concierge, a food delivery and restaurant service in the UAE. You help customers find information about restaurants, menu items, add-ons, branches, and other services.

CONTEXT INFORMATION:
${context}

${history ? `CONVERSATION HISTORY:\n${history}\n` : ''}

INSTRUCTIONS:
- Be friendly, helpful, and conversational
- Use the context information to provide accurate answers about restaurants, menu items, prices, and locations
- If you don't have specific information, be honest about it but try to be helpful
- Always mention prices in AED when available
- Suggest alternatives when possible
- Keep responses concise but informative
- If asked about locations, mention the specific branch names
- For food items, mention if they are vegetarian, non-vegetarian, or neither

USER MESSAGE: ${userMessage}

Please provide a helpful response based on the available information:`;
  }

  /**
   * Calculate simple text similarity (fallback when embeddings are not available)
   */
  public calculateTextSimilarity(text1: string, text2: string): number {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    
    const set1 = new Set(words1);
    const set2 = new Set(words2);
    
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size; // Jaccard similarity
  }

  /**
   * Extract keywords from text for better matching
   */
  public extractKeywords(text: string): string[] {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must'
    ]);

    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))
      .filter((word, index, arr) => arr.indexOf(word) === index); // Remove duplicates
  }

  /**
   * Health check for AI service
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Simple test to check if the service is working
      const testResponse = await this.generateResponse(
        'Hello',
        [],
        []
      );
      return testResponse.length > 0;
    } catch (error) {
      console.error('AI Service health check failed:', error);
      return false;
    }
  }
}
